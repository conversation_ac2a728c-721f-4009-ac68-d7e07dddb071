{"kind": "collectionType", "collectionName": "web_contents", "info": {"singularName": "web-content", "pluralName": "web-contents", "displayName": "Web content", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"slug": {"type": "string", "required": true, "regex": "^/", "unique": true}, "name": {"type": "string", "required": true, "private": true}, "title": {"type": "string", "required": true, "pluginOptions": {"i18n": {"localized": true}}}, "content": {"type": "dynamiczone", "components": ["web-content-parts.default", "web-content-parts.cards", "web-content-parts.heading", "web-content-parts.articles"], "pluginOptions": {"i18n": {"localized": true}}}, "web_menus": {"type": "relation", "relation": "oneToMany", "target": "api::web-menu.web-menu", "mappedBy": "page"}}}