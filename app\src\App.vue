<script setup lang="ts">
import { onMounted, watch } from 'vue';
import { RouterView, useRoute } from 'vue-router'
const route = useRoute();

import { useMenuContentStore } from "./stores/menuContent";
import { usePageContentStore } from './stores/pageContent';

const menuContentStore = useMenuContentStore();
const pageContentStore = usePageContentStore();

onMounted(() => {
  console.log("App mounted");
  menuContentStore.fetchMenuContent();
});

onMounted(async () => {
  const slug = route.path;
  console.log(slug);

  await pageContentStore.fetchPageContent(slug);

  console.log(pageContentStore.pageContent.data);
  if (!pageContentStore.pageContent.data) {
    console.log("No data");
    return;
  }

  document.title = pageContentStore.pageContent.data?.title;
});

// watch for route changes
watch(
  () => route.path,
  async (newPath, oldPath) => {
    console.log("Path changed", newPath, oldPath);
    await pageContentStore.fetchPageContent(newPath);

    // Update document title when route changes
    if (pageContentStore.pageContent.data?.title) {
      document.title = pageContentStore.pageContent.data.title;
    }
  },
);
</script>

<template>
  <RouterView />
</template>

<style scoped></style>
