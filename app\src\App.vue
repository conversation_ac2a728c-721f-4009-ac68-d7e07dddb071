<script setup lang="ts">
import { onMounted, watch, ref } from 'vue';
import { RouterView, useRoute } from 'vue-router'
const route = useRoute();

import { useMenuContentStore } from "./stores/menuContent";
import { usePageContentStore } from './stores/pageContent';

const menuContentStore = useMenuContentStore();
const pageContentStore = usePageContentStore();
const initialLoadComplete = ref(false);

onMounted(() => {
  console.log("App mounted");
  menuContentStore.fetchMenuContent();
});

onMounted(async () => {
  const slug = route.path;
  console.log("Initial load:", slug);

  await pageContentStore.fetchPageContent(slug);

  console.log(pageContentStore.pageContent.data);
  if (!pageContentStore.pageContent.data) {
    console.log("No data");
    return;
  }

  document.title = pageContentStore.pageContent.data?.title;
  initialLoadComplete.value = true;
});

// watch for route changes (only after initial load is complete)
watch(
  () => route.path,
  async (newPath, oldPath) => {
    // Skip if initial load hasn't completed yet
    if (!initialLoadComplete.value) {
      return;
    }

    console.log("Route changed:", oldPath, "->", newPath);
    await pageContentStore.fetchPageContent(newPath);

    // Update document title when route changes
    if (pageContentStore.pageContent.data?.title) {
      document.title = pageContentStore.pageContent.data.title;
    }
  },
);
</script>

<template>
  <RouterView />
</template>

<style scoped></style>
