<script setup lang="ts">
import type { DefaultOptions } from "./../types";

const props = defineProps<{
	options: DefaultOptions;
}>();
</script>

<template>
  <div class="flex flex-col h-full gap-4 w-full py-8 justify-center items-center">
    <h2 class="text-9xl">
      404
    </h2>
    <div>
      Co tady hled<PERSON>?
    </div>
    <router-link to="/" class="px-8 py-2 mt-8 cursor-pointer rounded-lg shadow-sm bg-[#61A0AF]
                    text-lg text-white">
      Zpět na domovskou stránku
    </router-link>
  </div>

</template>
