<script setup lang="ts">
const props = defineProps<{
    url: string;
}>();
</script>

<template>
    <div
        class="flex gap-8 flex-col flex-1 px-10 py-8 rounded-t-lg bg-white/30 border-b-8 border-[#61A0AF] shadow text-black">
        <div class="flex flex-col gap-4">
            <div>
                <h3 class="text-xl">
                    <slot name="title"></slot>
                </h3>
            </div>
            <div class="text-black/90 flex-1">
                <div class="text-justify">
                    <slot name="description"></slot>
                </div>
            </div>
        </div>

        <a class="cursor-pointer text-[hsl(196,50%,43%)] self-end hover:text-[hsl(196,50%,43%)]/80 transition"
            :href="props.url">
            <slot name="button">
                Learn More
            </slot>
        </a>
    </div>
</template>