{"collectionName": "components_web_content_parts_headings", "info": {"displayName": "Heading", "description": ""}, "options": {}, "attributes": {"content": {"type": "customField", "options": {"preset": "defaultHtml", "maxLengthWords": 255}, "required": true, "customField": "plugin::ckeditor5.CKEditor"}, "coverImage": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "links": {"displayName": "<PERSON>", "type": "component", "repeatable": true, "component": "web-content-components.link-button"}}}