const isMobileDevice = () => {
	const isMobile =
		/Android|webOS|iPhone|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
			navigator.userAgent,
		);
	const isiPad = /iPad/i.test(navigator.userAgent);

	if (isiPad) {
		const isLandscape = window.innerWidth > window.innerHeight;
		const isPortrait = window.innerWidth < window.innerHeight;
		const isSquare = window.innerWidth === window.innerHeight;
		if (isLandscape) {
			return false;
		} else if (isPortrait) {
			return true;
		} else if (isSquare) {
			return false;
		}
	}
	return isMobile;
};

export { isMobileDevice };
