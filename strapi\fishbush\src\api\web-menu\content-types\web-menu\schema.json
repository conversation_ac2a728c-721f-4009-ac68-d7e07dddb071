{"kind": "collectionType", "collectionName": "web_menus", "info": {"singularName": "web-menu", "pluralName": "web-menus", "displayName": "Web menu"}, "options": {"draftAndPublish": false}, "attributes": {"priority": {"type": "integer", "min": 0, "max": 1000}, "name": {"type": "string", "required": true}, "url": {"type": "string"}, "page": {"type": "relation", "relation": "manyToOne", "target": "api::web-content.web-content", "inversedBy": "web_menus"}, "type": {"type": "enumeration", "enum": ["MENU", "FOOTER"], "required": true, "default": "MENU"}}}