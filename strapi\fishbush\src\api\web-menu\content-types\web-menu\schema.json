{"kind": "collectionType", "collectionName": "web_menus", "info": {"singularName": "web-menu", "pluralName": "web-menus", "displayName": "Web menu", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"priority": {"type": "integer", "min": 0, "max": 1000, "pluginOptions": {"i18n": {"localized": true}}}, "name": {"type": "string", "required": true, "pluginOptions": {"i18n": {"localized": true}}}, "url": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "page": {"type": "relation", "relation": "manyToOne", "target": "api::web-content.web-content", "inversedBy": "web_menus"}, "type": {"type": "enumeration", "enum": ["MENU", "FOOTER"], "required": true, "default": "MENU", "pluginOptions": {"i18n": {"localized": true}}}}}