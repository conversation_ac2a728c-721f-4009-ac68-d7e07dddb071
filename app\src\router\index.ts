import { createRouter, createWebHistory } from 'vue-router'
import DesktopView from '../views/DesktopView.vue'
import MobileView from '../views/MobileView.vue'
import { isMobileDevice } from '../utils/mobile'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/:pathMatch(.*)*',
      name: 'home',
      component: isMobileDevice() ? MobileView : DesktopView,
    }
  ]
})

export default router
