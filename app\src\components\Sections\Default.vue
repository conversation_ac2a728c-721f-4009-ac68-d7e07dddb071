<script setup lang="ts">
import type { DefaultOptions } from "./types";

const props = defineProps<{
	options: DefaultOptions;
}>();
</script>

<template>
  <div class="flex flex-col gap-4 w-full py-8 items-center">
    <div class="flex flex-col w-[75%] border-l-10 border-[#61A0AF] bg-[#fff]/30 justify-center items-center py-16 rounded-r-lg shadow-lg">
      <div class="flex flex-col gap-4 w-4/5">
        <h2 class="header">
          {{ props.options.title }}
        </h2>

        <div class="flex flex-wrap gap-4">
          <div v-html="props.options.content" />
        </div>
      </div>
    </div>
  </div>

</template>
