import { defineStore } from "pinia";
import { ref } from "vue";
import { treaty } from "@elysiajs/eden";
import type { App } from "../../../server/src/index";


interface IMenuItemContent {
	priority: number;
	name: string;
	url: string;
	page: {
		slug: string;
	};
}

interface IArticlesContent {
	id: number;
	documentId?: string;
	header: string;
	slug: string;
	annotation: string;
	coverImage: any;
}

interface IPageContent {
	error?: string;
	data?: IArticlesContent[];
}

const client = treaty<App>("http://*********:3000");

export const useArticleContentStore = defineStore("articleContent", () => {
	const articles = ref<IPageContent>({ error: undefined, data: undefined });

	const fetchArticles = async () => {
		const r = await client.strapi["articles"].get();

		if (r.data === null || (r.data && "error" in r.data)) {
			articles.value = {
				error: r.data && "error" in r.data ? r.data.error : "Unknown error",
				data: undefined,
			};
			return;
		}

		articles.value.data = r.data;
	};

	return {
		articles,
		fetchArticles,
	};
});
