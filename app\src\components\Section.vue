<script setup lang="ts">
import { computed } from "vue";
import Default from "./Sections/Default.vue";
import FullBarColor from "./Sections/FullBarColor.vue";
import ExperienceCard from "./Sections/ExperienceCard.vue";
import NotFoundPage from "./Sections/errors/404.vue";
import Articles from "./Sections/Articles.vue";

import type {
	CardsOptions,
	DefaultOptions,
	HeaderOptions,
} from "./Sections/types";
import Header from "./Sections/Header.vue";

type Sections = "default" | "fullBarColor" | "cards" | "heading" | "articles" | "404";

interface FullBarColorConfig {
	title: string;
	color: string;
}

type SectionConfigMap = {
	default: DefaultOptions;
	fullBarColor: FullBarColorConfig;
	cards: CardsOptions;
	heading: HeaderOptions;
	"404": DefaultOptions;
	articles: DefaultOptions;
};

interface SectionProps<T extends Sections = Sections> {
	id: string;
	type: T;
	sectionConfig: SectionConfigMap[T];
}

const props = defineProps<SectionProps>();

const componentMap = {
	default: Default,
	fullBarColor: FullBarColor,
	cards: ExperienceCard,
	heading: Header,
	"404": NotFoundPage,
	articles: Articles,
} satisfies Record<Sections, any>;

const CurrentComponent = computed(() => componentMap[props.type]);
</script>

<template>
  <div
    class="flex flex-col gap-4 h-full w-full items-center"
    :id="props.id"
  >
    <component
      :is="CurrentComponent"
      :options="props.sectionConfig as any"
    />
  </div>
</template>
