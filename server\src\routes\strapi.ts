import { Elysia, t } from "elysia";
import { getMenuContent, getWebContent } from "../strapi/Strapi";

const strapi = new Elysia()
	.get(
		"/web-content",
		async ({ query }) => {
			const r = await getWebContent(query.slug);
			return r;
		},
		{
			query: t.Object({
				slug: t.String(),
			}),
		},
	)
	.get("/menu-content", async () => {
		const r = await getMenuContent();
		return r;
	})
	.get("/image", () => "test");

export default strapi;
