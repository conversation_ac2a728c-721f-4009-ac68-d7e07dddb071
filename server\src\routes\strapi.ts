import { Elysia, status, t } from "elysia";
import {
	getArticleContent,
	getArticles,
	getMenuContent,
	getWebContent,
} from "../strapi/Strapi";

const strapi = new Elysia()
	.get(
		"/web-content",
		async ({ query }) => {
			const r = await getWebContent(query.slug);
			if ("error" in r) {
				return new Response(r.error, { status: 404 });
			}
			return r;
		},
		{
			query: t.Object({
				slug: t.String(),
			}),
		},
	)
	.get("/menu-content", async () => {
		const r = await getMenuContent();
		return r;
	})
	.get(
		"/articles",
		async ({ query }) => {
			if (query.slug) {
				const r = await getArticleContent(query.slug);
				if ("error" in r) {
					return new Response(r.error, { status: 404 });
				}
				return r;
			}
			const r = await getArticles();
			return r;
		},
	)
	.get("/image", () => "test");

export default strapi;
