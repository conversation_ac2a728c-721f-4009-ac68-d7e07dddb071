<script setup lang="ts">
import Section from "../components/Section.vue";
import { usePageContentStore } from "../stores/pageContent";
import Menu from "../components/Menu.vue";

const pageContentStore = usePageContentStore();
</script>

<template>
  <!-- page wrapper: full viewport, now no page-scroll -->
  <div class="w-full min-w-64 h-screen bg-neutral-900 flex flex-col
           overflow-hidden">

    <Menu view="mobile"/>


    <!-- center column -->
    <div class="flex-1 flex justify-center min-h-0">
      <!-- white card with its own scrollbar -->
      <div class="bg-white/90 flex flex-col w-full flex-1 gap-16
               overflow-y-auto">
        <div class="flex flex-col flex-1">
          <Section v-for="section in pageContentStore.pageContent.data?.content" :key="section.id"
            :type="section.__component" :section-config="{ ...section }" :id="String(section.id)">
          </Section>
        </div>


        <div class="flex flex-col gap-4 w-full items-center">
          <div class="bg-neutral-900 text-white w-full flex flex-col gap-8 items-center py-8">
            <div class="flex gap-4 w-1/2">
              <div class="flex-1">
                First column
              </div>
              <div class="flex-1">
                Second column
              </div>
            </div>
            <div>
              &copy; Vojtěch Tmej 2025
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
