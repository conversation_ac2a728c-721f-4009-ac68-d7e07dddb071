<script setup lang="ts">
import Section from "../components/Section.vue";
import { usePageContentStore } from "../stores/pageContent";
import Menu from "../components/Menu.vue";

const currentHost = window.location.host;
const pageContentStore = usePageContentStore();
</script>

<template>
  <!-- page wrapper: full viewport, now no page-scroll -->
  <div class="w-full min-w-64 h-screen bg-neutral-900 px-8 pb-8 flex flex-col
           overflow-hidden">

    <!-- top bar -->
    <div class="bg-neutral-900 my-1 h-10 w-full flex items-center">
      <div class="text-white flex gap-2 flex-1">
        <!-- traffic-light buttons -->
        <div class="bg-red-500 rounded-full aspect-square p-0.5">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
            stroke="currentColor" class="size-4">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
          </svg>
        </div>
        <div class="bg-yellow-500 rounded-full aspect-square p-0.5">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
            stroke="currentColor" class="size-4">
            <path stroke-linecap="round" stroke-linejoin="round"
              d="M16.5 8.25V6a2.25 2.25 0 0 0-2.25-2.25H6A2.25 2.25 0 0 0 3.75 6v8.25A2.25 2.25 0 0 0 6 16.5h2.25m8.25-8.25H18a2.25 2.25 0 0 1 2.25 2.25V18A2.25 2.25 0 0 1 18 20.25h-7.5A2.25 2.25 0 0 1 8.25 18v-1.5m8.25-8.25h-6a2.25 2.25 0 0 0-2.25 2.25v6" />
          </svg>
        </div>
        <div class="bg-green-500 rounded-full aspect-square p-0.5">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
            stroke="currentColor" class="size-4">
            <path stroke-linecap="round" stroke-linejoin="round" d="M5 12h14" />
          </svg>
        </div>
      </div>
      <div class="w-96 rounded-lg border-2 border-white/40 flex justify-center items-center
               text-white/50 text-sm py-1 flex-1">
        {{ currentHost }}
      </div>
      <div class="flex-1" />
    </div>

    <Menu view="desktop"/>


    <!-- center column -->
    <div class="flex-1 flex justify-center min-h-0">
      <!-- white card with its own scrollbar -->
      <div class="bg-white/90 rounded-lg flex flex-col w-full flex-1 gap-16
               overflow-y-auto">
        <div class="flex flex-col flex-1">
          <Section v-for="section in pageContentStore.pageContent.data?.content" :key="section.id"
            :type="section.__component" :section-config="{ ...section }" :id="String(section.id)">
          </Section>
        </div>


        <div class="flex flex-col gap-4 w-full items-center">
          <div class="bg-neutral-900 text-white w-full flex flex-col gap-8 items-center py-8">
            <div class="flex gap-4 w-1/2">
              <div class="flex-1">
                First column
              </div>
              <div class="flex-1">
                Second column
              </div>
            </div>
            <div>
              &copy; Vojtěch Tmej 2025
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
