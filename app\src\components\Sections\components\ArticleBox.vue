<script setup lang="ts">
import { RouterLink, useRoute } from "vue-router";

const route = useRoute();

const props = defineProps<{
	header: string;
	description: string;
	url: string;
	imageUrl: string;
}>();
</script>

<template>
    <RouterLink :to="`${route.fullPath}${props.url}`" class="flex flex-col gap-4 w-full py-4 items-center bg-white/30 rounded-xl shadow hover:scale-101  transition">
        <div class="flex w-full px-4 gap-4">
            <div class="flex w-fit">
                <div class="flex w-[300px] aspect-video rounded-lg"
                    :style="{ backgroundImage: `url(${props.imageUrl})`, backgroundSize: 'cover', backgroundPosition: 'center' }" />
            </div>
            <div class="w-full">
                <h2 class="text-2xl">
                    {{ props.header }}
                </h2>
                <div class="flex flex-wrap gap-4">
                    <div v-html="props.description" />
                </div>
            </div>
        </div>
    </RouterLink>
</template>