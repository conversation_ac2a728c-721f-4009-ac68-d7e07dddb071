import { defineStore } from "pinia";
import { ref } from "vue";
import { treaty } from "@elysiajs/eden";
import type { App } from "../../../server/src/index";


interface IMenuItemContent {
	priority: number;
	name: string;
	url: string;
	page: {
		slug: string;
	};
}

interface IMenuContent {
	menu?: IMenuItemContent[];
	footer?: IMenuItemContent[];
}

interface IPageContent {
	error?: string;
	data?: IMenuContent;
}

const client = treaty<App>("localhost:3000");

export const useMenuContentStore = defineStore("menuContent", () => {
	const menuContent = ref<IPageContent>({ error: undefined, data: undefined });

	const fetchMenuContent = async () => {
		const r = await client.strapi["menu-content"].get();

		if (r.data === null || (r.data && "error" in r.data)) {
			menuContent.value = {
				error: r.data && "error" in r.data ? r.data.error : "Unknown error",
				data: undefined,
			};
			return;
		}

		menuContent.value.data = r.data;
	};

	return {
		menuContent,
		fetchMenuContent,
	};
});
