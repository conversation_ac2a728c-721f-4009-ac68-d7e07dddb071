<script setup lang="ts">
import { computed } from "vue";
import type { HeaderOptions } from "./types";
import { isMobileDevice } from "../../utils/mobile";

const props = defineProps<{
  options: HeaderOptions;
}>();

const isMobile = isMobileDevice();

const coverImage = computed(
  () => `url(http://*********:1337${props.options.coverImage.url})`,
);
</script>

<template>
  <div
    :class="['flex flex-col gap-4 w-full items-center justify-center dark bg-image-custom mb-8 shadow-lg relative rounded-b-xl', isMobile ? 'h-svh' : 'h-[calc(100vh-2.5rem-2rem)]']">
    <!-- social links, bottom‑right -->
    <div :class="['absolute flex gap-4 bottom-8', isMobile ? 'left-1/2 -translate-x-1/2' : 'right-8']">

      <div v-for="link in props.options.links" class="relative p-[1px] rounded-full overflow-hidden hover:scale-105 transition">
        <!-- animated highlight -->
        <div class="pointer-events-none absolute inset-0
                before:absolute before:inset-0
                before:bg-[radial-gradient(circle_at_40%_40%,rgba(255,255,255,0.35)_0%,transparent_60%)]
                before:animate-[slow-sway_8s_linear_infinite]"></div>

        <a :href="link.url" class="relative z-10 flex items-center px-4 py-1 rounded-full
              bg-white/10 backdrop-blur-md
              border border-white/25 shadow-[0_4px_10px_rgba(0,0,0,0.25)]
              text-white/90 hover:text-white">
          {{ link.name }}
        </a>
      </div>

    </div>

    <div class="flex flex-col w-fit drop-shadow-lg">
      <div class="flex flex-col w-fit">
        <div class="self-end text-neutral-100 text-lg">I am</div>
        <div class="bg-[#61A0AF] text-white pr-4 pl-12 py-2 text-2xl w-fit shadow-lg">
          Vojta
        </div>
      </div>
      <div class="bg-[#96C9DC] text-white ml-12 pl-4 pr-12 py-2 text-2xl shadow-lg">
        UX designer <br /> &amp; Fullstack developer
      </div>
    </div>

    <div class="w-2/5 text-white" v-html="props.options.content" />

    <!-- <a href="#experiences" class="px-8 py-2 mt-8 cursor-pointer rounded-lg shadow-sm bg-[#61A0AF]
                    text-lg text-white">
      Get To Know More
    </a> -->
  </div>
</template>

<style scoped>
.bg-image-custom {
  background-image: linear-gradient(to bottom left,
      hsla(192, 33%, 33%, 0.6),
      rgba(150, 201, 200, 0.2)
      /* amber‑900/40 */
      /* amber‑600/50 */
    ),
    v-bind(coverImage);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
</style>