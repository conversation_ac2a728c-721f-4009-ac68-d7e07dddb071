import { defineStore } from "pinia";
import { ref } from "vue";
import { treaty } from "@elysiajs/eden";
import type { App } from "./../../../server/src/index";

interface IPageContent {
	error?: string;
	data?: {
		id: number;
		documentId: string;
		slug: string;
		title: string;
		locale: string;
		content: any[];
	};
}

const client = treaty<App>("http://*********:3000");

export const usePageContentStore = defineStore("pageContent", () => {
	const pageContent = ref<IPageContent>({ error: undefined, data: undefined });

	const fetchPageContent = async (slug: string) => {
		const r = await client.strapi["web-content"].get({ query: { slug: slug } });

		if (r.data === null || (r.data && "error" in r.data)) {
			pageContent.value = {
				error: r.data && "error" in r.data ? r.data.error : "Unknown error",
				data: undefined,
			};

			if (r.status === 404) {
				pageContent.value.data = {
					id: 0,
					documentId: "",
					slug: "404",
					title: "404",
					locale: "en",
					content: [
						{
							__component: "404",
							id: 0,
							Header: "404",
							Content: "404",
						},
					],
				};
			}

			return;
		}

		pageContent.value.data = r.data;
	};

	return {
		pageContent,
		fetchPageContent,
	};
});
