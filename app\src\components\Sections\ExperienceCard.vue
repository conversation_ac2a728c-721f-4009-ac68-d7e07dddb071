<script setup lang="ts">
import { isMobileDevice } from "../../utils/mobile";
import SkillBox from "../SkillBox.vue";
import type { CardsOptions } from "./types";

const props = defineProps<{
	options: CardsOptions;
}>();

const isMobile = isMobileDevice();
</script>

<template>
  <div class="flex flex-col gap-4 w-[75%] py-8">
    <h2
      class="header"
    >
      {{ props.options.title }}
    </h2>

    <div class="flex flex-col flex-wrap gap-4">
      <div class="w-full" v-html="props.options.content"></div>
      <div :class="['flex flex-wrap gap-8 mt-4', isMobile ? 'flex-col' : 'flex-row']">
        <SkillBox v-for="card in props.options.experienceCards" :key="card.id" :url="card.url">
          <template #title>{{ card.title }}</template>
            <template #description>
              <div v-html="card.description"></div>
            </template>
            <template #button>{{ card.urlText }}</template>
        </SkillBox>
      </div>
    </div>
  </div>
</template>
