{"name": "app", "version": "1.0.50", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "bun run --watch src/index.ts"}, "dependencies": {"@elysiajs/cors": "^1.3.3", "@types/qs": "^6.14.0", "add": "^2.0.6", "axios": "^1.10.0", "bun": "^1.2.19", "qs": "^6.14.0"}, "devDependencies": {"bun-types": "latest", "@biomejs/biome": "^2.1.2"}, "module": "src/index.js", "type": "module", "peerDependencies": {"typescript": "^5.0.0"}}