export interface DefaultOptions {
	title: string;
	content: string;
}

export interface ExperienceCardOptions {
    id: number;
	title: string;
	description: string;
	url: string;
	urlText: string;
}

export interface CardsOptions {
	title: string;
	content: string;
	experienceCards: ExperienceCardOptions[];
}

export interface HeaderOptions {
	content: string;
	coverImage: any;
	links: {
		name: string;
		url: string;
	}[];
}
