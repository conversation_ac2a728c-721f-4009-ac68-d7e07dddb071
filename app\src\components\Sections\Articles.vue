<script setup lang="ts">
import type { DefaultOptions } from "./types";
import { useArticleContentStore } from "../../stores/articleContent";
import ArticleBox from "./components/ArticleBox.vue";
import { computed, onMounted } from "vue";

const articleContentStore = useArticleContentStore();

onMounted(async () => {
	await articleContentStore.fetchArticles();
});

const articles = computed(() => articleContentStore.articles.data);

const props = defineProps<{
	options: DefaultOptions;
}>();
</script>

<template>
  <div class="flex flex-col gap-4 w-full py-8 items-center">
    <div class="flex flex-col w-[65%] gap-8">
      <ArticleBox v-for="article in articles" :key="article.id" :header="article.header" :description="article.annotation" :url="article.slug" :imageUrl="article.coverImage?.url" />
    </div>
  </div>

</template>
