<template>
  <div ref="wrapper" class="fixed top-14 left-1/2 -translate-x-1/2 z-50 w-fit rounded-full p-[1px] overflow-hidden">
    <div
      class="pointer-events-none absolute inset-0 before:absolute before:inset-0">
    </div>
    <nav :class="navClasses"
      class="relative z-10 flex h-8 items-center gap-4 px-8 rounded-full text-sm font-medium transition-colors duration-150">
      <router-link v-for="item in menuContentStore.menuContent.data?.menu" :to="item.url" :class="['hover:opacity-80 transition', currentPath === item.url ? 'font-bold' : '']">{{ item.name }}</router-link>
    </nav>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue";
import { useMenuContentStore } from "../stores/menuContent";
import router from "../router";

const currentPath = ref(window.location.pathname);
const menuContentStore = useMenuContentStore();

// Create canvas context for color parsing
let ctx: CanvasRenderingContext2D | null = null;
try {
	const canvas = document.createElement("canvas");
	ctx = canvas.getContext("2d");
} catch {
	// Fallback if canvas is not available
}

const wrapper = ref<HTMLElement | null>(null);
const lightBg = ref(false);
let rafId = 0;
let frameCount = 0;

function toRGB(col: string): [number, number, number] {
	try {
		if (!ctx) return [255, 255, 255];
		ctx.fillStyle = "#000";
		ctx.fillStyle = col;
		const nums = ctx.fillStyle.match(/\d+/g)?.map(Number);
		if (nums && nums.length >= 3) return [nums[0]!, nums[1]!, nums[2]!];
	} catch {}
	return [255, 255, 255];
}
function isLight(col: string): boolean {
	const [r, g, b] = toRGB(col);
	return 0.299 * r + 0.587 * g + 0.114 * b > 140;
}
function effectiveBg(el: HTMLElement | null): string {
	while (el) {
		const bg = getComputedStyle(el).backgroundColor;
		if (bg && !/rgba?\([^)]+,\s*0\)/.test(bg) && bg !== "transparent")
			return bg;
		el = el.parentElement;
	}
	return "rgb(255 255 255)";
}
function themeClass(el: HTMLElement | null): "light" | "dark" | null {
	while (el) {
		if (el.classList.contains("light")) return "light";
		if (el.classList.contains("dark")) return "dark";
		el = el.parentElement;
	}
	return null;
}

function sample() {
	const w = wrapper.value;
	if (!w) {
		rafId = requestAnimationFrame(sample);
		return;
	}

	// Throttle sampling to every 3 frames to reduce flickering
	frameCount++;
	if (frameCount % 3 !== 0) {
		rafId = requestAnimationFrame(sample);
		return;
	}

	const { left, top, width, height } = w.getBoundingClientRect();
	const cx = left + width / 2,
		cy = top + height / 2;

	// Temporarily hide the element to sample what's behind it
	const originalPointerEvents = w.style.pointerEvents;
	const originalVisibility = w.style.visibility;

	w.style.pointerEvents = "none";
	w.style.visibility = "hidden";

	const under = document.elementFromPoint(cx, cy) as HTMLElement | null;

	// Restore the element immediately
	w.style.pointerEvents = originalPointerEvents;
	w.style.visibility = originalVisibility;

	const theme = themeClass(under);
	const newLightBg =
		theme === "light"
			? true
			: theme === "dark"
				? false
				: isLight(effectiveBg(under));

	// Only update if the value actually changed to reduce unnecessary re-renders
	if (lightBg.value !== newLightBg) {
		lightBg.value = newLightBg;
	}

	rafId = requestAnimationFrame(sample);
}

onMounted(() => {
	rafId = requestAnimationFrame(sample);
});
onUnmounted(() => cancelAnimationFrame(rafId));

const navClasses = computed(() =>
	lightBg.value
		? "bg-black/10 text-black mix-blend-difference border border-black/20 backdrop-blur-xl backdrop-brightness-110"
		: "bg-white/20 text-white mix-blend-difference border border-white/25 backdrop-blur-xl backdrop-brightness-95",
);
</script>

<style scoped></style>
