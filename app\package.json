{"name": "package", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --port 5000 --host 0.0.0.0", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@tailwindcss/vite": "^4.1.11", "axios": "^1.10.0", "biome": "^0.3.3", "gsap": "^3.13.0", "pinia": "^2.1.7", "tailwindcss-intersect": "^2.2.0", "tailwindcss-motion": "^1.1.1", "vue": "^3.4.29", "vue-i18n": "11", "vue-router": "^4.3.3"}, "devDependencies": {"@rushstack/eslint-patch": "^1.8.0", "@tailwindcss/postcss": "^4.1.11", "@tsconfig/node20": "^20.1.4", "@types/jsdom": "^21.1.7", "@types/node": "^20.14.5", "@vitejs/plugin-vue": "^5.0.5", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.5.1", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "jsdom": "^24.1.0", "npm-run-all2": "^6.2.0", "postcss": "^8.4.39", "prettier": "^3.2.5", "tailwindcss": "^4.1.11", "typescript": "~5.4.0", "vite": "^5.3.1", "vitest": "^1.6.0", "vue-tsc": "^2.0.21"}}